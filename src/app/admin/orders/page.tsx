"use client";

import { useEffect, useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import {
  searchOrders,
  getOrdersWithPagination
} from "@/lib/firebase/firestore";
import { Order, OrderStatus, PaymentMethod } from "@/types/models";
import { formatDate } from "@/lib/utils";
import { DocumentSnapshot } from "firebase/firestore";
import OrderDetailsModal from "@/components/admin-dashboard/OrderDetailsModal";

export default function AdminOrdersPage() {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [lastVisible, setLastVisible] = useState<DocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const pageSize = 20;

  // Load orders
  useEffect(() => {
    if (user?.uid) {
      fetchOrders(true);
    }
  }, [user?.uid, selectedStatus]);

  // Handle search
  useEffect(() => {
    handleSearch();
  }, [searchTerm, orders]);

  const fetchOrders = async (reset: boolean = true) => {
    try {
      setIsLoading(reset);

      const statusFilter = selectedStatus === 'all' ? undefined : selectedStatus as OrderStatus;
      const { orders: orderData, lastVisible: newLastVisible } = await getOrdersWithPagination(
        reset ? undefined : lastVisible || undefined,
        pageSize,
        statusFilter
      );

      if (reset) {
        setOrders(orderData);
        setFilteredOrders(orderData);
        setCurrentPage(1);
      } else {
        setOrders(prev => [...prev, ...orderData]);
        setFilteredOrders(prev => [...prev, ...orderData]);
        setCurrentPage(prev => prev + 1);
      }

      setLastVisible(newLastVisible);
      setHasMore(orderData.length === pageSize);
    } catch (error) {
      console.error("Error fetching orders:", error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setFilteredOrders(orders);
      return;
    }

    try {
      const searchResults = await searchOrders(searchTerm);
      setFilteredOrders(searchResults);
    } catch (error) {
      console.error("Error searching orders:", error);
    }
  };

  const getOrderStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return isClient ? t("orders.status.orderPlaced") : "Order Placed";
      case OrderStatus.PREPARING:
        return isClient ? t("orders.status.preparing") : "Preparing";
      case OrderStatus.READY_FOR_PICKUP:
        return isClient ? t("orders.status.readyForPickup") : "Ready for Pickup";
      case OrderStatus.OUT_FOR_DELIVERY:
        return isClient ? t("orders.status.outForDelivery") : "Out for Delivery";
      case OrderStatus.DELIVERED:
        return isClient ? t("orders.status.delivered") : "Delivered";
      case OrderStatus.CANCELLED:
        return isClient ? t("orders.status.cancelled") : "Cancelled";
      default:
        return status;
    }
  };

  const getStatusBadgeClass = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500";
      case OrderStatus.PREPARING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      case OrderStatus.READY_FOR_PICKUP:
        return "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-500";
      case OrderStatus.OUT_FOR_DELIVERY:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500";
      case OrderStatus.DELIVERED:
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case OrderStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  const getPaymentMethodLabel = (method: PaymentMethod): string => {
    switch (method) {
      case PaymentMethod.CREDIT_CARD:
        return isClient ? t("orders.paymentMethods.creditCard") : "Credit Card";
      case PaymentMethod.DEBIT_CARD:
        return isClient ? t("orders.paymentMethods.debitCard") : "Debit Card";
      case PaymentMethod.CASH:
        return isClient ? t("orders.paymentMethods.cash") : "Cash";
      case PaymentMethod.GIFT_CARD:
        return isClient ? t("orders.paymentMethods.giftCard") : "Gift Card";
      case PaymentMethod.LOYALTY_POINTS:
        return isClient ? t("orders.paymentMethods.loyaltyPoints") : "Loyalty Points";
      default:
        return method;
    }
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsModalOpen(true);
  };

  const handleOrderUpdated = (updatedOrder: Order) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === updatedOrder.id ? updatedOrder : order
      )
    );
    setFilteredOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === updatedOrder.id ? updatedOrder : order
      )
    );
  };

  const handleLoadMore = async () => {
    if (!hasMore || isLoadingMore) return;

    setIsLoadingMore(true);
    await fetchOrders(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isClient ? t('admin.orders') : 'Orders Management'}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {isClient ? t('admin.manageOrders') : 'View and manage all customer orders'}
          </p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Status Filter */}
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {isClient ? t('admin.filterByStatus') : 'Filter by Status'}
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as OrderStatus | 'all')}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-[#242832] text-gray-900 dark:text-gray-100"
            >
              <option value="all">{isClient ? t('admin.allOrders') : 'All Orders'}</option>
              <option value={OrderStatus.ORDER_PLACED}>{getOrderStatusLabel(OrderStatus.ORDER_PLACED)}</option>
              <option value={OrderStatus.PREPARING}>{getOrderStatusLabel(OrderStatus.PREPARING)}</option>
              <option value={OrderStatus.READY_FOR_PICKUP}>{getOrderStatusLabel(OrderStatus.READY_FOR_PICKUP)}</option>
              <option value={OrderStatus.OUT_FOR_DELIVERY}>{getOrderStatusLabel(OrderStatus.OUT_FOR_DELIVERY)}</option>
              <option value={OrderStatus.DELIVERED}>{getOrderStatusLabel(OrderStatus.DELIVERED)}</option>
              <option value={OrderStatus.CANCELLED}>{getOrderStatusLabel(OrderStatus.CANCELLED)}</option>
            </select>
          </div>

          {/* Search */}
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {isClient ? t('admin.searchOrders') : 'Search Orders'}
            </label>
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={isClient ? t('admin.searchPlaceholder') : 'Search by order ID, items, or address...'}
                className="w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-[#242832] text-gray-900 dark:text-gray-100"
              />
              <i className="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm overflow-hidden">
        {isLoading ? (
          <div className="flex justify-center p-16">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : filteredOrders.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-[#242832]">
                  <tr className="text-left text-sm text-gray-500 dark:text-gray-400">
                    <th className="px-6 py-4 font-medium">{isClient ? t('admin.orderId') : 'Order ID'}</th>
                    <th className="px-6 py-4 font-medium">{isClient ? t('admin.date') : 'Date'}</th>
                    <th className="px-6 py-4 font-medium">{isClient ? t('admin.items') : 'Items'}</th>
                    <th className="px-6 py-4 font-medium">{isClient ? t('admin.total') : 'Total'}</th>
                    <th className="px-6 py-4 font-medium">{isClient ? t('admin.payment') : 'Payment'}</th>
                    <th className="px-6 py-4 font-medium">{isClient ? t('admin.statusColumn') : 'Status'}</th>
                    <th className="px-6 py-4 font-medium">{isClient ? t('admin.actionColumn') : 'Actions'}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredOrders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50 dark:hover:bg-[#242832]">
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          #{order.id.slice(-8).toUpperCase()}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {order.createdAt instanceof Date
                            ? formatDate(order.createdAt, true)
                            : typeof order.createdAt === 'string'
                              ? formatDate(new Date(order.createdAt), true)
                              : ''}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {order.items[0]?.name}{order.items.length > 1 && ` +${order.items.length - 1} more`}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {isClient ? `${t('common.currency')} ${order.total.toFixed(2)}` : `SAR ${order.total.toFixed(2)}`}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {getPaymentMethodLabel(order.paymentMethod)}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(order.status)}`}>
                          {getOrderStatusLabel(order.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewOrder(order)}
                            className="text-[#56999B] dark:text-[#5DBDC0] hover:text-[#74C8CA] text-sm"
                          >
                            {isClient ? t('admin.view') : 'View'}
                          </button>
                          <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 text-sm">
                            {isClient ? t('admin.edit') : 'Edit'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="flex justify-center p-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={handleLoadMore}
                  disabled={isLoadingMore}
                  className="px-6 py-2 bg-[#56999B] hover:bg-[#74C8CA] text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoadingMore ? (
                    <>
                      <i className="fa-solid fa-spinner fa-spin mr-2"></i>
                      {isClient ? t('common.loading') : 'Loading...'}
                    </>
                  ) : (
                    <>
                      <i className="fa-solid fa-chevron-down mr-2"></i>
                      {isClient ? t('admin.loadMore') : 'Load More'}
                    </>
                  )}
                </button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-16">
            <i className="fa-solid fa-receipt text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {isClient ? t('admin.noOrders') : 'No Orders Found'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm 
                ? (isClient ? t('admin.noSearchResults') : 'No orders match your search criteria.')
                : (isClient ? t('admin.noOrdersYet') : 'No orders have been placed yet.')
              }
            </p>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      <OrderDetailsModal
        order={selectedOrder}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onOrderUpdated={handleOrderUpdated}
      />
    </div>
  );
}
