{"common": {"barcodeCafe": "Barcode Cafe", "footer": "2025 Barcode Cafe. All rights reserved.", "allRightsReserved": "All rights reserved.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "signOut": "Sign Out", "logout": "Logout", "cancel": "Cancel", "search": "Search", "loading": "Loading...", "updating": "Updating...", "deleting": "Deleting...", "cafeTagline": "Barcode Speciality Coffee", "cafeDescription": "Premium coffee and delicious treats in the heart of KSA", "close": "Close", "kcal": "kcal", "done": "Done", "remove": "Remove", "currency": "SAR", "min": "min", "back": "Back", "deleteButton": "Delete", "caffeine": "caffeine", "allergic": "allergens"}, "signin": {"title": "Welcome Back!", "subtitle": "Sign in to your account", "emailLabel": "Email address", "emailPlaceholder": "Enter your email", "passwordLabel": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot password?", "signInButton": "Sign In", "orSignInWith": "Or sign in with", "noAccount": "Don't have an account?", "signUp": "Sign up now", "rememberMe": "Remember me"}, "signup": {"title": "Join the Barcode Cafe Community!", "subtitle": "Create your account to get started", "fullNameLabel": "Full Name", "fullNamePlaceholder": "Enter your full name", "emailLabel": "Email Address", "emailPlaceholder": "Enter your email", "passwordLabel": "Password", "passwordPlaceholder": "Create a password", "confirmPasswordLabel": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "termsCheckbox": "I agree to the", "termsLink": "Terms & Conditions", "createAccountButton": "Create Account", "orSignUpWith": "Or sign up with", "haveAccount": "Already have an account?", "signIn": "Sign in here"}, "nav": {"dashboard": "Dashboard", "orderHistory": "Order History", "giftCards": "Gift Cards", "addresses": "Addresses", "reviews": "Reviews", "settings": "Settings", "menu": "<PERSON><PERSON>"}, "menu": {"title": "Our Menu", "description": "Explore our delicious offerings", "categories": "Categories", "categoryDescription": "Browse our selection by category", "noItems": "No menu items found in this category.", "featured": "Featured Item", "stockStatus": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock"}, "cart": "<PERSON><PERSON>", "viewCart": "View Cart", "addToCart": "Add to Cart", "addedToCart": "Added to <PERSON><PERSON>", "add": "Add", "quantity": "Quantity", "total": "Total", "subtotal": "Subtotal", "clearCart": "Clear Cart", "placeOrder": "Place Order", "placingOrder": "Placing Order...", "emptyCart": "Your cart is empty", "searchResults": "Search Results", "noSearchResults": "No results found. Try a different search term."}, "checkout": {"deliveryOptions": "Delivery Options", "tableNumber": "Table Number", "enterTableNumber": "Enter Table Number", "selectTableZone": "Select Table Zone", "selectTableNumber": "Select Table Number", "selectTable": "Select a table", "tableZone": "Table Zone", "noTableZones": "No table zones available", "noTablesAvailable": "No tables available", "noDeliveryZones": "No delivery zones available", "tablePlaceholder": "e.g., A12", "pickUp": "Pick Up", "delivery": "Delivery", "selectDeliveryZone": "Select Delivery Zone", "deliveryFee": "Delivery Fee", "deliveryAddress": "Delivery Address", "addressPlaceholder": "Enter your full address", "proceedToDelivery": "Proceed to Delivery Options", "proceedToPayment": "Proceed to Payment", "confirmOrder": "Confirm Order", "orderSummary": "Order Summary", "deliveryType": "Delivery Type", "paymentMethod": "Payment Method", "cashOnDelivery": "Cash on Delivery/Pickup", "deliverySummary": "Delivery Summary", "deliveryZone": "Delivery Zone", "pickUpFromCounter": "Pick up from counter"}, "admin": {"dashboard": "Dashboard", "menuItems": "Menu Items", "categories": "Categories", "offers": "Offers & Discounts", "inventory": "Inventory", "deliveryZones": {"title": "Delivery Zones", "description": "Manage your delivery zones and pickup locations", "tabs": {"all": "All Zones", "pickUp": "Pick Up", "delivery": "Delivery", "inHouseTables": "In-House Tables"}}, "paymentSettings": "Payment Settings", "reviewsSection": "Reviews", "qrGeneratorMenu": "QR Generator", "qrGeneratorDescription": "Generate QR codes for your cafe menu and tables", "qrGeneratorComingSoon": "QR code generator coming soon", "generateQR": "Generate QR", "viewMenuItems": "View Menu Items", "manageCategories": "Manage Categories", "qrGenerator": {"generateNew": "Generate New QR Code", "codeType": "QR Code Type", "menuItem": "<PERSON><PERSON>", "tableNumber": "Table Number", "specialOffer": "Special Offer", "size": "Size (px)", "contentUrl": "Content/URL", "urlPlaceholder": "Enter URL or content for QR code", "foregroundColor": "Foreground Color", "backgroundColor": "Background Color", "generateButton": "Generate QR Code", "preview": "Preview", "download": "Download", "print": "Print", "recentCodes": "Recently Generated QR Codes", "table": "Table", "specialMenu": "Special Menu", "breakfastMenu": "Breakfast Menu", "weekendOffers": "Weekend Offers", "generatedOn": "Generated on"}, "manageCafe": "Manage Cafe", "totalOrders": "Total Orders", "totalRevenue": "Total Revenue", "totalCustomers": "Total Customers", "recentOrders": "Recent Orders", "topProducts": "Top Products", "orderStatusSection": "Order Status", "pendingReviewsCount": "Pending Reviews", "noDataMessage": "No data available yet", "viewAll": "View All", "welcomeMessage": "Welcome back! Here's what's happening with your cafe today.", "activeMenuItems": "Active Menu Items", "customerReviews": "Customer Reviews", "quickActions": "Quick Actions", "addItemMenu": "Add <PERSON>u <PERSON>em", "newOffer": "<PERSON> Offer", "syncInventory": "Sync Inventory", "viewReviews": "View Reviews", "orderId": "Order ID", "customer": "Customer", "items": "Items", "itemsCount": "items", "itemCount": "item", "total": "Total", "statusColumn": "Status", "actionColumn": "Action", "view": "View", "menuItemsDescription": "Manage your cafe's menu items", "manageMenuItems": "Manage your restaurant menu items", "addNewItem": "Add New Item", "searchMenuItems": "Search menu items...", "allCategories": "All Categories", "categoriesDescription": "Manage and organize your menu categories", "addCategory": "Add Category", "addNewCategory": "Add New Category", "clickToAddCategory": "Click to add a new menu category", "categoryStatistics": "Category Statistics", "totalCategories": "Total Categories", "activeCategories": "Active Categories", "featuredCategories": "Featured Categories", "availableHours": "Available: {{hours}}", "categoryName": "Category Name", "icon": "Icon", "availableFrom": "Available From", "availableTo": "Available To", "isActive": "Active", "isVisible": "Visible", "isFeatured": "Featured", "save": "Save", "editButton": "Edit", "cancelButton": "Cancel", "editCategory": "Edit Category", "deleteCategoryConfirmation": "Delete Category Confirmation", "deleteCategoryConfirmationMessage": "Are you sure you want to delete this category? This action cannot be undone.", "noCategories": "No categories found. Add your first category to get started!", "categoryCreated": "Category Created", "categoryCreatedDescription": "{{name}} has been created successfully.", "categoryUpdated": "Category Updated", "categoryUpdatedDescription": "{{name}} has been updated successfully.", "categoryDeleted": "Category Deleted", "categoryDeletedDescription": "{{name}} has been deleted successfully.", "errorFetchingCategories": "Error Fetching Categories", "errorSavingCategory": "Error Saving Category", "errorDeletingCategory": "Error Deleting Category", "iconOptions": {"hotBeverage": "Hot Beverage", "burger": "Burger", "pizza": "Pizza", "dessert": "Dessert", "utensils": "Utensils", "drinks": "Drinks", "iceCream": "Ice Cream", "fish": "Fish", "chicken": "Chicken", "preview": "Preview"}, "itemsCategories": {"hotDrinks": "Hot Drinks", "coldDrinks": "Cold Drinks", "desserts": "Desserts", "hotBeverages": "Hot Beverages", "mainCourse": "Main Course"}, "statusAll": "Status: All", "statusActive": "Active", "statusInactive": "Inactive", "inStock": "In Stock", "lowStock": "Low Stock", "editItem": "<PERSON>em", "previous": "Previous", "next": "Next", "itemsMenu": {"cappuccino": "Cappuccino", "cappuccinoDesc": "Rich espresso topped with creamy milk foam", "icedCaramelMacchiato": "Iced <PERSON><PERSON>", "icedCaramelMacchiatoDesc": "Espresso with vanilla and caramel", "chocolateCake": "Chocolate Cake", "chocolateCakeDesc": "Rich chocolate cake with fresh berries"}, "orderStatusTypes": {"completed": "Completed", "processing": "Processing", "new": "New"}, "stats": {"today": "Today's Sales", "thisWeek": "This Week", "thisMonth": "This Month", "thisYear": "This Year"}, "navigation": {"manageCafe": "Manage Cafe", "dashboard": "Dashboard", "menuItems": "Menu Items", "categories": "Categories", "offersAndDiscounts": "Offers & Discounts", "inventory": "Inventory", "deliveryZonesNav": "Delivery Zones", "paymentSettings": "Payment Settings", "reviews": "Reviews", "qrGenerator": "QR Generator"}, "addMenuItem": "Add <PERSON>u <PERSON>em", "outOfStock": "Out of Stock", "deleteItem": "Delete Item", "noMenuItems": "No menu items found. Add your first item!", "loadMore": "Load More", "editMenuItem": {"title": "<PERSON> <PERSON>", "description": "Update your menu item details", "cancel": "Cancel", "itemName": "Item Name", "category": "Category", "price": "Price ($)", "prepTime": "Preparation Time (mins)", "stock": "Stock Quantity", "itemDescription": "Description", "itemImage": "Item Image", "imageURL": "Image URL", "status": "Status", "active": "Active", "featured": "Featured Item", "deliveryAvailable": "Available for Delivery", "saveChanges": "Save Changes"}, "deliveryZoneTypes": {"pickUp": "Pick Up", "delivery": "Delivery", "inHouseTables": "In-House Tables"}, "zoneName": "Zone Name", "zoneNamePlaceholder": "Enter zone name", "zoneType": "Zone Type", "zoneDetails": "Details", "deliveryZoneStatus": "Status", "deliveryZoneActions": "Actions", "deliveryZoneActive": "Active", "deliveryZoneInactive": "Inactive", "deliveryZoneEdit": "Edit", "deliveryZoneDelete": "Delete", "confirmDelete": "Confirm Delete", "deleteZoneConfirmation": "Are you sure you want to delete this zone? This action cannot be undone.", "noZonesMessage": "No delivery zones available yet. Add your first zone below.", "addZone": "Add Zone", "updateZone": "Update Zone", "deliveryZoneDescription": "Description", "descriptionPlaceholder": "Enter description (optional)", "deliverySettings": "Delivery Settings", "deliveryFee": "Delivery Fee", "minOrder": "Min Order Amount", "radius": "Radius (km)", "estimatedTime": "Est. Time (min)", "tableSettings": "Table Settings", "tableNumberPlaceholder": "Enter table number", "addTable": "Add", "tables": "Tables", "noTablesAdded": "No tables added yet", "tableNumberExists": "Table number already exists", "nameRequired": "Name is required", "tableNumbersRequired": "At least one table number is required", "errorSavingZone": "Error saving delivery zone", "orders": "Orders", "manageOrders": "View and manage all customer orders", "filterByStatus": "Filter by Status", "allOrders": "All Orders", "searchOrders": "Search Orders", "searchPlaceholder": "Search by order ID, items, or address...", "date": "Date", "payment": "Payment", "edit": "Edit", "noOrders": "No Orders Found", "noSearchResults": "No orders match your search criteria.", "noOrdersYet": "No orders have been placed yet.", "deliveryInfo": "Delivery Information", "updateStatus": "Update Status", "editOrder": "Edit Order", "cannotEditOrder": "Cannot Edit Order", "orderItems": "Order Items", "selectMenuItem": "Select a menu item to add", "deliveryInformation": "Delivery Information", "saveChanges": "Save Changes"}, "customer": {"memberSince": "Member since {{date}}", "editProfile": "Edit Profile", "loyaltyPoints": "Loyalty Points", "totalOrders": "Total Orders", "giftCards": {"title": "Gift Cards", "subtitle": "Purchase new gift cards or manage your existing ones", "purchaseNew": "Purchase New Gift Card", "classicDesign": "Classic Design", "festiveDesign": "Festive Design", "selectAmount": "Select Amount", "customAmount": "Custom Amount", "purchaseButton": "Purchase Gift Card", "yourGiftCards": "Your Gift Cards", "cardEndingIn": "Card ending in", "balance": "Balance", "viewDetails": "View Details", "share": "Share", "shareSuccess": "Gift card shared successfully!", "fromFriend": "A friend", "shareGreeting": "🎉 Check out this amazing gift card! 🎁\n\nCard Number: {{cardNumber}}\nBalance: {{balance}}\nExpires: {{expiryDate}}", "shareGreetingExtended": "🎉 Surprise! {{senderName}} just sent you a gift card from {{cafeName}}!\n\n🎁 Gift Card Details:\n🔢 Card Number: {{cardNumber}}\n💰 Balance: {{balance}}\n📅 Expires: {{expiryDate}}\n\nTo redeem, just show this card number at Barcode Cafe or enter it in the app. Enjoy your treat! 😄", "shareGreetingEmojiSafe": "Surprise! {{senderName}} just sent you a gift card from {{cafeName}}!\n\n:: Gift Card Details ::\nCard Number: {{cardNumber}}\nBalance: {{balance}}\nExpires: {{expiryDate}}\n\nTo redeem, just show this card number at Barcode Cafe or enter it in the app. Enjoy your treat!", "shareCard": "Share Gift Card", "shareMessage": "Here's your gift card message:", "copyToClipboard": "Copy to clipboard", "copied": "Copied!", "shareWhatsApp": "Share via WhatsApp", "redeemGiftCard": "Redeem Gift Card", "enterGiftCardNumber": "Enter Gift Card Number", "enterPIN": "Enter PIN", "redeemButton": "Redeem Card", "noCardsYet": "You don't have any gift cards yet.", "expiresOn": "Expires on", "processing": "Processing...", "invalidAmount": "Please enter a valid amount", "purchaseSuccess": "Gift card purchased successfully!", "purchaseError": "There was an error purchasing your gift card. Please try again.", "invalidCode": "Please enter a valid gift card code", "cardNotFound": "Gift card not found", "alreadyRedeemed": "You have already redeemed this gift card", "redeemSuccess": "Gift card redeemed successfully!", "redeemError": "There was an error redeeming your gift card. Please try again."}, "recentOrders": "Recent Orders", "viewAll": "View All", "loyaltyProgramStatus": "Loyalty Program Status", "progressToGoldStatus": "Progress to Gold Status", "loyaltyProgressMessage": "Earn {{points}} more points to reach Gold Status and unlock exclusive rewards!", "profile": "Customer Profile", "dashboard": {"welcome": "Welcome to your Dashboard"}, "items": {"caramelMacchiato": "<PERSON><PERSON>", "espressoDoubleShot": "Espresso Double Shot", "chocolateCroissant": "Chocolate Croissant", "orderPlaced": "Order Placed", "more": "more"}, "profileEdit": {"title": "Edit Profile", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "streetAddress": "Street Address", "city": "City", "state": "State", "postalCode": "Postal Code", "country": "Country", "profilePicture": "Change Photo", "maxFileSize": "Maximum file size: 2MB", "preferences": "Preferences", "emailNotifs": "Email Notifications", "emailNotifsDesc": "Receive order updates and promotions", "smsNotifs": "SMS Notifications", "smsNotifsDesc": "Get text messages for order status", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "enterStreetAddress": "Enter your street address", "enterCity": "Enter city", "enterState": "Enter state", "enterPostalCode": "Enter postal code", "enterCountry": "Enter country", "cancel": "Cancel", "saveChanges": "Save Changes", "emailNotEditable": "Email address cannot be changed", "saving": "Saving..."}, "emptyStates": {"orders": {"title": "No Orders Yet", "message": "You haven't placed any orders yet. Check out our menu to discover our delicious offerings!", "action": "<PERSON><PERSON><PERSON> Menu"}, "giftCards": {"title": "No Gift Cards", "message": "You don't have any gift cards yet. Purchase a gift card to enjoy later or share with friends and family.", "action": "Get Gift Card"}, "addresses": {"title": "No Saved Addresses", "message": "You don't have any saved addresses. Add your delivery addresses for faster checkout.", "action": "Add Address"}, "reviews": {"title": "No Reviews", "message": "You haven't written any reviews yet. Share your experience with our products!", "action": "Write Review"}, "recentOrders": {"title": "No Recent Orders", "message": "You haven't placed any orders recently. Check out our menu to order something delicious!", "action": "<PERSON><PERSON><PERSON> Menu"}, "settings": {"title": "Settings", "message": "Manage your account settings including notifications, language, and theme preferences.", "action": "Update Settings"}}}, "forgotPassword": {"title": "Forgot Password", "subtitle": "Enter your email address and we'll send you a password reset link", "emailLabel": "Email address", "emailPlaceholder": "Enter your email", "submitButton": "Send Reset Link", "backToLogin": "Back to login", "successMessage": "If your email exists in our system, you will receive a password reset link shortly.", "errorMessage": "Please enter a valid email address."}, "addresses": {"name": "Name", "namePlaceholder": "e.g., Home, Work", "addressLine1": "Address Line 1", "addressLine1Placeholder": "Street address, P.O. box", "addressLine2": "Address Line 2", "addressLine2Placeholder": "Apartment, suite, unit, building, floor, etc.", "city": "City", "cityPlaceholder": "Enter city", "state": "State/Province", "statePlaceholder": "Enter state or province", "postalCode": "Postal Code", "postalCodePlaceholder": "Enter postal code", "country": "Country", "countryPlaceholder": "Enter country", "makeDefault": "Make this my default address", "default": "<PERSON><PERSON><PERSON>", "addAddress": "Add Address", "editAddress": "Edit Address", "saveChanges": "Save Changes", "deleteConfirmTitle": "Delete Address", "deleteConfirmMessage": "Are you sure you want to delete this address? This action cannot be undone.", "delete": "Delete"}, "reviews": {"rating": "Rating", "comment": "Comment", "commentPlaceholder": "Tell us about your experience... What did you like or dislike?", "submitReview": "Submit Review", "updateReview": "Update Review", "writeReview": "Write a Review", "edit": "Edit", "delete": "Delete", "deleteConfirmTitle": "Delete Review", "deleteConfirmMessage": "Are you sure you want to delete this review? This action cannot be undone.", "pastOrders": "Past Orders You Can Review", "reviewOrder": "Review {{orderName}}", "editReview": "Edit Review", "alreadyReviewed": "Already Reviewed", "reviewThisOrder": "Write a Review", "title": "Reviews", "titlePlaceholder": "Brief summary of your experience", "content": "Description", "contentPlaceholder": "Tell us more about your experience in detail", "addReview": "Add Review", "saveChanges": "Save Changes"}, "orders": {"status": {"orderPlaced": "Order placed", "preparing": "Preparing", "readyForPickup": "Ready for pickup", "outForDelivery": "Out for delivery", "delivered": "Delivered", "cancelled": "Cancelled"}, "items": "Items", "viewDetails": "View Details", "specialInstructions": "Special Instructions", "orderDetails": "Order Details", "orderNumber": "Order Number", "paymentMethod": "Payment Method", "paymentMethods": {"creditCard": "Credit Card", "debitCard": "Debit Card", "cash": "Cash", "giftCard": "Gift Card", "loyaltyPoints": "Loyalty Points"}, "summary": "Order Summary", "subtotal": "Subtotal", "tax": "Tax", "total": "Total", "printReceipt": "Print Receipt", "cancelOrder": "Cancel Order", "notFound": {"title": "Order Not Found", "message": "The order you're looking for doesn't exist or you don't have permission to view it."}, "backToOrders": "Back to Orders"}}